#!/usr/bin/env python3
"""
知识库自动化测试系统主程序
提供命令行接口来运行各种测试功能
"""

import argparse
import sys
import os
from pathlib import Path

from kb_test_controller import KBTestController
from config_manager import config_manager


def setup_argparser():
    """设置命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="知识库自动化测试系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 生成测试集
  python main.py generate-testset --input data/document.pdf
  
  # 批量生成测试集
  python main.py generate-testsets --input-dir data/
  
  # 运行单个测试
  python main.py test --testset testsets/document_testset.json
  
  # 批量测试
  python main.py batch-test --testsets-dir testsets/
  
  # 运行完整流水线
  python main.py pipeline --input data/ --force-regenerate
        """
    )
    
    parser.add_argument(
        '--config', 
        type=str, 
        default='config.yaml',
        help='配置文件路径 (默认: config.yaml)'
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 生成测试集命令
    gen_parser = subparsers.add_parser('generate-testset', help='从单个文件生成测试集')
    gen_parser.add_argument('--input', required=True, help='输入文件路径')
    gen_parser.add_argument('--output', help='输出文件路径（可选）')
    
    # 批量生成测试集命令
    batch_gen_parser = subparsers.add_parser('generate-testsets', help='批量生成测试集')
    batch_gen_parser.add_argument('--input-dir', help='输入目录路径（默认使用配置文件中的路径）')
    batch_gen_parser.add_argument('--output-dir', help='输出目录路径（默认使用配置文件中的路径）')
    
    # 单个测试命令
    test_parser = subparsers.add_parser('test', help='运行单个测试集')
    test_parser.add_argument('--testset', required=True, help='测试集文件路径')
    test_parser.add_argument('--name', help='测试名称（可选）')
    test_parser.add_argument('--sync', action='store_true', help='使用同步模式（默认异步）')
    test_parser.add_argument('--no-llm-eval', action='store_true', help='不使用LLM评估（默认使用）')
    
    # 批量测试命令
    batch_test_parser = subparsers.add_parser('batch-test', help='批量运行测试集')
    batch_test_parser.add_argument('--testsets-dir', help='测试集目录路径（默认使用配置文件中的路径）')
    batch_test_parser.add_argument('--sync', action='store_true', help='使用同步模式（默认异步）')
    batch_test_parser.add_argument('--no-llm-eval', action='store_true', help='不使用LLM评估（默认使用）')
    
    # 完整流水线命令
    pipeline_parser = subparsers.add_parser('pipeline', help='运行完整测试流水线')
    pipeline_parser.add_argument('--input', required=True, help='输入源（文件或目录路径）')
    pipeline_parser.add_argument('--force-regenerate', action='store_true', help='强制重新生成测试集')
    pipeline_parser.add_argument('--sync', action='store_true', help='使用同步模式（默认异步）')
    pipeline_parser.add_argument('--no-llm-eval', action='store_true', help='不使用LLM评估（默认使用）')
    
    # 查看历史命令
    history_parser = subparsers.add_parser('history', help='查看测试历史')
    history_parser.add_argument('--limit', type=int, default=10, help='显示记录数量（默认10）')
    
    # 验证配置命令
    validate_parser = subparsers.add_parser('validate-config', help='验证配置文件')
    
    return parser


def main():
    """主函数"""
    parser = setup_argparser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        # 初始化控制器
        controller = KBTestController(args.config)
        
        # 验证配置
        if not config_manager.validate_config():
            print("❌ 配置文件验证失败，请检查配置")
            return 1
        
        # 执行相应命令
        if args.command == 'generate-testset':
            print(f"📝 开始生成测试集: {args.input}")
            result = controller.generate_testset_from_file(args.input, args.output)
            if result:
                print(f"✅ 测试集生成成功: {result}")
            else:
                print("❌ 测试集生成失败")
                return 1
                
        elif args.command == 'generate-testsets':
            print("📝 开始批量生成测试集")
            results = controller.generate_testsets_from_directory(args.input_dir, args.output_dir)
            if results:
                print(f"✅ 批量生成完成，共生成 {len(results)} 个测试集")
                for result in results:
                    print(f"   - {result}")
            else:
                print("❌ 批量生成失败")
                return 1
                
        elif args.command == 'test':
            print(f"🧪 开始运行测试: {args.testset}")
            result = controller.run_single_test(
                testset_path=args.testset,
                test_name=args.name,
                use_async=not args.sync,
                use_llm_evaluation=not args.no_llm_eval
            )
            if result:
                stats = result.get('statistics', {})
                print(f"✅ 测试完成")
                print(f"   总问题数: {stats.get('total_questions', 0)}")
                print(f"   成功率: {stats.get('success_rate', 0):.1%}")
                print(f"   平均总分: {stats.get('average_scores', {}).get('overall', 0):.2f}")
                print(f"   报告文件: {list(result.get('generated_reports', {}).values())}")
            else:
                print("❌ 测试失败")
                return 1
                
        elif args.command == 'batch-test':
            print("🧪 开始批量测试")
            results = controller.run_batch_tests(
                testsets_dir=args.testsets_dir,
                use_async=not args.sync,
                use_llm_evaluation=not args.no_llm_eval
            )
            if results:
                print(f"✅ 批量测试完成，共完成 {len(results)} 个测试")
                total_questions = sum(r.get('statistics', {}).get('total_questions', 0) for r in results)
                avg_success_rate = sum(r.get('statistics', {}).get('success_rate', 0) for r in results) / len(results)
                avg_overall_score = sum(r.get('statistics', {}).get('average_scores', {}).get('overall', 0) for r in results) / len(results)
                
                print(f"   总问题数: {total_questions}")
                print(f"   平均成功率: {avg_success_rate:.1%}")
                print(f"   平均总分: {avg_overall_score:.2f}")
            else:
                print("❌ 批量测试失败")
                return 1
                
        elif args.command == 'pipeline':
            print(f"🚀 开始运行完整流水线: {args.input}")
            results = controller.run_full_pipeline(
                input_source=args.input,
                force_regenerate_testsets=args.force_regenerate,
                use_async=not args.sync,
                use_llm_evaluation=not args.no_llm_eval
            )
            if results:
                print(f"✅ 流水线执行完成，共完成 {len(results)} 个测试")
            else:
                print("❌ 流水线执行失败")
                return 1
                
        elif args.command == 'history':
            print("📊 测试历史记录:")
            history = controller.get_test_history(args.limit)
            if history:
                for i, record in enumerate(history, 1):
                    stats = record.get('statistics', {})
                    print(f"{i:2d}. {record.get('test_name', '未知测试')} "
                          f"({record.get('test_date', '未知时间')[:19]})")
                    print(f"     成功率: {stats.get('success_rate', 0):.1%}, "
                          f"总分: {stats.get('average_scores', {}).get('overall', 0):.2f}")
            else:
                print("   暂无测试历史记录")
                
        elif args.command == 'validate-config':
            print("🔍 验证配置文件...")
            if config_manager.validate_config():
                print("✅ 配置文件验证通过")
            else:
                print("❌ 配置文件验证失败")
                return 1
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return 1
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
