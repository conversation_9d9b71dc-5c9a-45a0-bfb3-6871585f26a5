"""
答案评估系统模块
负责评估知识库返回答案的质量
"""

import re
import json
import logging
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from difflib import SequenceMatcher
import dashscope
from dashscope import Generation
from config_manager import config_manager


@dataclass
class EvaluationScore:
    """评估分数数据类"""
    similarity: float
    relevance: float
    completeness: float
    accuracy: float
    overall: float
    reason: str


class AnswerEvaluator:
    """答案评估器类"""
    
    def __init__(self):
        """初始化答案评估器"""
        self.config = config_manager.get_evaluation_config()
        api_config = config_manager.get_api_config('dashscope')
        dashscope.api_key = api_config.get('api_key')
        self.model = api_config.get('model', 'qwen-max')
        self.similarity_threshold = self.config.get('similarity_threshold', 0.8)
        self.logger = logging.getLogger(__name__)
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            相似度分数 (0-1)
        """
        if not text1 or not text2:
            return 0.0
        
        # 使用SequenceMatcher计算基础相似度
        matcher = SequenceMatcher(None, text1.lower(), text2.lower())
        basic_similarity = matcher.ratio()
        
        # 计算关键词重叠度
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 and not words2:
            return 1.0
        elif not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        keyword_similarity = len(intersection) / len(union) if union else 0.0
        
        # 综合相似度 (基础相似度权重0.6，关键词相似度权重0.4)
        combined_similarity = basic_similarity * 0.6 + keyword_similarity * 0.4
        
        return round(combined_similarity, 3)
    
    def evaluate_with_llm(self, question: str, expected_answer: str, 
                         actual_answer: str) -> EvaluationScore:
        """
        使用LLM评估答案质量
        
        Args:
            question: 问题
            expected_answer: 期望答案
            actual_answer: 实际答案
            
        Returns:
            评估分数
        """
        try:
            prompt_template = self.config.get('evaluation_prompt_template', '')
            prompt = prompt_template.format(
                question=question,
                expected_answer=expected_answer,
                actual_answer=actual_answer
            )
            
            response = Generation.call(
                model=self.model,
                prompt=prompt
            )
            
            if response.status_code == 200:
                evaluation_text = response.output.text
                return self._parse_evaluation_result(evaluation_text)
            else:
                self.logger.error(f"LLM评估失败: {response.message}")
                return self._fallback_evaluation(expected_answer, actual_answer)
                
        except Exception as e:
            self.logger.error(f"LLM评估异常: {e}")
            return self._fallback_evaluation(expected_answer, actual_answer)
    
    def _parse_evaluation_result(self, evaluation_text: str) -> EvaluationScore:
        """
        解析LLM评估结果
        
        Args:
            evaluation_text: LLM返回的评估文本
            
        Returns:
            评估分数
        """
        try:
            # 使用正则表达式提取分数
            similarity_match = re.search(r'相似度[：:]\s*(\d+(?:\.\d+)?)', evaluation_text)
            relevance_match = re.search(r'相关性[：:]\s*(\d+(?:\.\d+)?)', evaluation_text)
            completeness_match = re.search(r'完整性[：:]\s*(\d+(?:\.\d+)?)', evaluation_text)
            accuracy_match = re.search(r'准确性[：:]\s*(\d+(?:\.\d+)?)', evaluation_text)
            overall_match = re.search(r'总体评分[：:]\s*(\d+(?:\.\d+)?)', evaluation_text)
            reason_match = re.search(r'评价理由[：:]\s*(.+)', evaluation_text, re.DOTALL)
            
            similarity = float(similarity_match.group(1)) / 10 if similarity_match else 0.5
            relevance = float(relevance_match.group(1)) / 10 if relevance_match else 0.5
            completeness = float(completeness_match.group(1)) / 10 if completeness_match else 0.5
            accuracy = float(accuracy_match.group(1)) / 10 if accuracy_match else 0.5
            overall = float(overall_match.group(1)) / 10 if overall_match else 0.5
            reason = reason_match.group(1).strip() if reason_match else "无详细评价"
            
            return EvaluationScore(
                similarity=similarity,
                relevance=relevance,
                completeness=completeness,
                accuracy=accuracy,
                overall=overall,
                reason=reason
            )
            
        except Exception as e:
            self.logger.error(f"解析评估结果失败: {e}")
            return EvaluationScore(0.5, 0.5, 0.5, 0.5, 0.5, "解析失败")
    
    def _fallback_evaluation(self, expected_answer: str, actual_answer: str) -> EvaluationScore:
        """
        备用评估方法（当LLM评估失败时使用）
        
        Args:
            expected_answer: 期望答案
            actual_answer: 实际答案
            
        Returns:
            评估分数
        """
        if not actual_answer.strip():
            return EvaluationScore(0.0, 0.0, 0.0, 0.0, 0.0, "实际答案为空")
        
        similarity = self.calculate_text_similarity(expected_answer, actual_answer)
        
        # 简单的启发式评估
        relevance = similarity  # 相似度可以作为相关性的代理
        completeness = min(len(actual_answer) / max(len(expected_answer), 1), 1.0)
        accuracy = similarity  # 相似度也可以作为准确性的代理
        overall = (similarity + relevance + completeness + accuracy) / 4
        
        return EvaluationScore(
            similarity=similarity,
            relevance=relevance,
            completeness=completeness,
            accuracy=accuracy,
            overall=overall,
            reason="使用备用评估方法"
        )
    
    def evaluate_answer(self, question: str, expected_answer: str, 
                       actual_answer: str, use_llm: bool = True) -> EvaluationScore:
        """
        评估答案质量
        
        Args:
            question: 问题
            expected_answer: 期望答案
            actual_answer: 实际答案
            use_llm: 是否使用LLM评估
            
        Returns:
            评估分数
        """
        if use_llm:
            return self.evaluate_with_llm(question, expected_answer, actual_answer)
        else:
            return self._fallback_evaluation(expected_answer, actual_answer)
    
    def batch_evaluate(self, test_results: List[Dict[str, Any]], 
                      use_llm: bool = True) -> List[Dict[str, Any]]:
        """
        批量评估测试结果
        
        Args:
            test_results: 测试结果列表
            use_llm: 是否使用LLM评估
            
        Returns:
            包含评估分数的测试结果列表
        """
        evaluated_results = []
        
        for i, result in enumerate(test_results):
            self.logger.info(f"评估进度: {i+1}/{len(test_results)}")
            
            if not result.get('success', False):
                # 如果测试失败，直接给0分
                evaluation = EvaluationScore(0.0, 0.0, 0.0, 0.0, 0.0, "测试失败")
            else:
                evaluation = self.evaluate_answer(
                    question=result['question'],
                    expected_answer=result['expected_answer'],
                    actual_answer=result['actual_answer'],
                    use_llm=use_llm
                )
            
            # 添加评估结果到原结果中
            result_with_evaluation = result.copy()
            result_with_evaluation['evaluation'] = {
                'similarity': evaluation.similarity,
                'relevance': evaluation.relevance,
                'completeness': evaluation.completeness,
                'accuracy': evaluation.accuracy,
                'overall': evaluation.overall,
                'reason': evaluation.reason
            }
            
            evaluated_results.append(result_with_evaluation)
        
        return evaluated_results
    
    def calculate_statistics(self, evaluated_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算评估统计信息
        
        Args:
            evaluated_results: 包含评估分数的结果列表
            
        Returns:
            统计信息字典
        """
        if not evaluated_results:
            return {}
        
        # 提取所有评估分数
        similarities = []
        relevances = []
        completenesses = []
        accuracies = []
        overalls = []
        success_count = 0
        
        for result in evaluated_results:
            if result.get('success', False):
                success_count += 1
            
            eval_data = result.get('evaluation', {})
            similarities.append(eval_data.get('similarity', 0))
            relevances.append(eval_data.get('relevance', 0))
            completenesses.append(eval_data.get('completeness', 0))
            accuracies.append(eval_data.get('accuracy', 0))
            overalls.append(eval_data.get('overall', 0))
        
        total_count = len(evaluated_results)
        
        return {
            'total_questions': total_count,
            'successful_queries': success_count,
            'success_rate': success_count / total_count if total_count > 0 else 0,
            'average_scores': {
                'similarity': sum(similarities) / len(similarities) if similarities else 0,
                'relevance': sum(relevances) / len(relevances) if relevances else 0,
                'completeness': sum(completenesses) / len(completenesses) if completenesses else 0,
                'accuracy': sum(accuracies) / len(accuracies) if accuracies else 0,
                'overall': sum(overalls) / len(overalls) if overalls else 0
            },
            'score_distribution': {
                'excellent': len([s for s in overalls if s >= 0.8]),
                'good': len([s for s in overalls if 0.6 <= s < 0.8]),
                'fair': len([s for s in overalls if 0.4 <= s < 0.6]),
                'poor': len([s for s in overalls if s < 0.4])
            }
        }
