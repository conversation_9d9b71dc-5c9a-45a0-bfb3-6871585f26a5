import time
from ragas.testset import TestsetGenerator
from langchain.text_splitter import RecursiveCharacterTextSplitter
# 加载你的知识库文档
from langchain_community.document_loaders import PyMuPDFLoader
from langchain_community.llms import DashScope
from langchain_community.embeddings import DashScopeEmbeddings

from testset_generator import TestSet
from dashscope import Generation

print("--- 开始加载文档 ---")
start_time = time.time()
loader = PyMuPDFLoader("C:\\Users\\<USER>\\work\\install_soft\\dify\\document\\3.pdf")
documents = loader.load()
load_time = time.time()
print(f"--- 文档加载完成，耗时: {load_time - start_time:.2f} 秒 ---")


# 分割文档
print("\n--- 开始分割文档 ---")
text_splitter = RecursiveCharacterTextSplitter(chunk_size=2048, chunk_overlap=500)
documents = text_splitter.split_documents(documents)
split_time = time.time()
print(f"--- 文档分割完成，耗时: {split_time - load_time:.2f} 秒 ---")


# 打印每个分块
for i, doc in enumerate(documents):
    print(f"--- 分块 {i+1} ---")
    print(doc.page_content)
    print("-" * (len(f"--- 分块 {i+1} ---")))

# 初始化测试集生成器
print("\n--- 开始生成测试集 ---")

# 初始化 LLM
llm = DashScope(
    model="qwen-turbo",  # 具体模型名以 dashscope 支持为准
    api_key="sk-2374f1a49b0548c8bba5fa63d85d04a5"
)

# 初始化 Embedding
embedding_model = DashScopeEmbeddings(
    model="text-embedding-v1",  # 具体模型名以 dashscope 支持为准
    api_key="sk-2374f1a49b0548c8bba5fa63d85d04a5"
)

generator = TestsetGenerator(llm=llm, embedding_model=embedding_model)

# 生成测试集
testset = generator.generate(documents, test_size=10)
generate_time = time.time()
print(f"--- 测试集生成完成，耗时: {generate_time - split_time:.2f} 秒 ---")

print("\n\n--- 生成的测试集 ---")
print(testset.to_pandas())
print("--- 测试集结束 ---")

end_time = time.time()
print(f"\n--- 总执行时间: {end_time - start_time:.2f} 秒 ---")

print("2")