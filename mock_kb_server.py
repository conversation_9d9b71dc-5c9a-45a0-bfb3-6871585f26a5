#!/usr/bin/env python3
"""
模拟知识库服务器
用于演示和测试知识库自动化测试系统
"""

import json
import time
import random
from typing import Dict, Any, List
from flask import Flask, request, jsonify
from difflib import SequenceMatcher
import logging

app = Flask(__name__)

# 模拟知识库数据
KNOWLEDGE_BASE = [
    {
        "id": 1,
        "content": "DISP员工持股计划是一种让员工能够持有公司股份的计划，旨在激励员工积极参与公司的发展，并分享公司的成长成果。",
        "keywords": ["DISP", "员工持股计划", "股份", "激励", "发展", "成长"]
    },
    {
        "id": 2,
        "content": "太平洋商业保险将于2025年1月1日起实施新政策，替代之前的太平商业保险。",
        "keywords": ["太平洋商业保险", "2025年", "新政策", "太平商业保险"]
    },
    {
        "id": 3,
        "content": "全面薪酬管理包括基本工资、奖金、福利与健康计划等多个方面，是一种综合性的员工激励策略。",
        "keywords": ["全面薪酬管理", "基本工资", "奖金", "福利", "健康计划", "激励策略"]
    },
    {
        "id": 4,
        "content": "招聘流程包括人才招聘、招聘信念、招聘策略、招聘指南以及职位发布流程等环节。",
        "keywords": ["招聘流程", "人才招聘", "招聘信念", "招聘策略", "招聘指南", "职位发布"]
    },
    {
        "id": 5,
        "content": "Transformation Camp提供战略思维和项目管理两种类型的培训课程。",
        "keywords": ["Transformation Camp", "战略思维", "项目管理", "培训课程"]
    },
    {
        "id": 6,
        "content": "员工自愿投资DISP计划时，公司会提供相当于个人出资额15倍的奖金作为激励。",
        "keywords": ["DISP", "自愿投资", "15倍", "奖金", "激励"]
    },
    {
        "id": 7,
        "content": "参与DISP计划的员工需要拥有全职劳动合同，且入职时间不晚于2024年10月31日。",
        "keywords": ["DISP", "全职劳动合同", "2024年10月31日", "入职时间"]
    },
    {
        "id": 8,
        "content": "员工最大认缴限额为其2024年度相关收入的25%。",
        "keywords": ["认缴限额", "2024年度", "收入", "25%"]
    }
]


def calculate_similarity(text1: str, text2: str) -> float:
    """计算两个文本的相似度"""
    return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()


def search_knowledge_base(query: str, top_k: int = 5, score_threshold: float = 0.1) -> List[Dict[str, Any]]:
    """
    在知识库中搜索相关内容
    
    Args:
        query: 查询问题
        top_k: 返回结果数量
        score_threshold: 分数阈值
        
    Returns:
        搜索结果列表
    """
    results = []
    
    for item in KNOWLEDGE_BASE:
        # 计算与内容的相似度
        content_similarity = calculate_similarity(query, item["content"])
        
        # 计算与关键词的相似度
        keyword_similarities = [calculate_similarity(query, keyword) for keyword in item["keywords"]]
        max_keyword_similarity = max(keyword_similarities) if keyword_similarities else 0
        
        # 综合分数
        score = content_similarity * 0.7 + max_keyword_similarity * 0.3
        
        if score >= score_threshold:
            results.append({
                "id": item["id"],
                "content": item["content"],
                "score": score,
                "keywords": item["keywords"]
            })
    
    # 按分数排序并返回top_k个结果
    results.sort(key=lambda x: x["score"], reverse=True)
    return results[:top_k]


def generate_answer(query: str, search_results: List[Dict[str, Any]]) -> str:
    """
    基于搜索结果生成答案
    
    Args:
        query: 查询问题
        search_results: 搜索结果
        
    Returns:
        生成的答案
    """
    if not search_results:
        return "抱歉，我无法找到相关信息来回答您的问题。"
    
    # 使用最相关的结果生成答案
    best_result = search_results[0]
    
    if best_result["score"] > 0.6:
        # 高相似度，直接返回内容
        return best_result["content"]
    elif best_result["score"] > 0.3:
        # 中等相似度，基于内容生成答案
        return f"根据相关信息，{best_result['content']}"
    else:
        # 低相似度，返回模糊答案
        return f"根据现有信息，可能与以下内容相关：{best_result['content']}"


@app.route('/api/query', methods=['POST'])
def query_knowledge_base():
    """知识库查询API端点"""
    try:
        data = request.get_json()
        
        if not data or 'question' not in data:
            return jsonify({
                'error': '缺少必需的参数: question'
            }), 400
        
        question = data['question']
        top_k = data.get('top_k', 5)
        score_threshold = data.get('score_threshold', 0.1)
        
        # 模拟响应延迟
        time.sleep(random.uniform(0.1, 0.5))
        
        # 搜索知识库
        search_results = search_knowledge_base(question, top_k, score_threshold)
        
        # 生成答案
        answer = generate_answer(question, search_results)
        
        # 模拟偶尔的失败情况
        if random.random() < 0.05:  # 5%的失败率
            return jsonify({
                'error': '服务暂时不可用'
            }), 503
        
        return jsonify({
            'question': question,
            'answer': answer,
            'search_results': search_results,
            'metadata': {
                'response_time': random.uniform(0.1, 0.5),
                'model_version': 'mock-v1.0',
                'confidence': search_results[0]['score'] if search_results else 0
            }
        })
        
    except Exception as e:
        app.logger.error(f"查询处理失败: {e}")
        return jsonify({
            'error': f'内部服务器错误: {str(e)}'
        }), 500


@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({
        'status': 'healthy',
        'timestamp': time.time(),
        'knowledge_base_size': len(KNOWLEDGE_BASE)
    })


@app.route('/api/stats', methods=['GET'])
def get_stats():
    """获取统计信息端点"""
    return jsonify({
        'knowledge_base_size': len(KNOWLEDGE_BASE),
        'available_endpoints': ['/api/query', '/api/health', '/api/stats'],
        'version': '1.0.0'
    })


if __name__ == '__main__':
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    print("🚀 启动模拟知识库服务器...")
    print("📚 知识库包含以下内容:")
    for i, item in enumerate(KNOWLEDGE_BASE, 1):
        print(f"   {i}. {item['content'][:50]}...")
    
    print("\n🌐 API端点:")
    print("   POST /api/query - 查询知识库")
    print("   GET  /api/health - 健康检查")
    print("   GET  /api/stats - 统计信息")
    
    print("\n📝 查询示例:")
    print("   curl -X POST http://localhost:3000/api/query \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"question\": \"什么是DISP员工持股计划？\"}'")
    
    print("\n✅ 服务器启动在 http://localhost:3000")
    
    app.run(host='0.0.0.0', port=3000, debug=True)
