#!/usr/bin/env python3
"""
知识库自动化测试系统演示脚本
展示完整的测试流程
"""

import os
import time
import subprocess
import threading
from pathlib import Path

from kb_test_controller import KBTestController
from config_manager import config_manager


def start_mock_server():
    """启动模拟知识库服务器"""
    print("🚀 启动模拟知识库服务器...")
    try:
        # 在后台启动模拟服务器
        process = subprocess.Popen(
            ['python', 'mock_kb_server.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待服务器启动
        time.sleep(3)
        
        # 检查服务器是否正常运行
        import requests
        try:
            response = requests.get('http://localhost:3000/api/health', timeout=5)
            if response.status_code == 200:
                print("✅ 模拟服务器启动成功")
                return process
            else:
                print("❌ 模拟服务器启动失败")
                return None
        except requests.exceptions.RequestException:
            print("❌ 无法连接到模拟服务器")
            return None
            
    except Exception as e:
        print(f"❌ 启动模拟服务器失败: {e}")
        return None


def demo_testset_generation():
    """演示测试集生成功能"""
    print("\n" + "="*60)
    print("📝 演示1: 测试集生成")
    print("="*60)
    
    controller = KBTestController()
    
    # 检查是否有现有的测试集
    testsets_dir = config_manager.get('paths.testsets', 'testsets')
    existing_testsets = []
    if os.path.exists(testsets_dir):
        existing_testsets = [f for f in os.listdir(testsets_dir) if f.endswith('.json')]
    
    if existing_testsets:
        print(f"✅ 发现 {len(existing_testsets)} 个现有测试集:")
        for testset in existing_testsets:
            print(f"   - {testset}")
        return existing_testsets
    else:
        print("📝 未发现现有测试集，开始生成...")
        
        # 检查data目录中的文件
        data_dir = config_manager.get('paths.input_documents', 'data')
        if not os.path.exists(data_dir):
            print(f"❌ 数据目录不存在: {data_dir}")
            return []
        
        files = [f for f in os.listdir(data_dir) 
                if f.endswith(('.pdf', '.docx', '.html', '.htm'))]
        
        if not files:
            print(f"❌ 在 {data_dir} 目录中未找到支持的文档文件")
            return []
        
        print(f"📁 发现 {len(files)} 个文档文件:")
        for file in files:
            print(f"   - {file}")
        
        # 生成测试集
        try:
            generated_testsets = controller.generate_testsets_from_directory()
            print(f"✅ 成功生成 {len(generated_testsets)} 个测试集")
            return [os.path.basename(f) for f in generated_testsets]
        except Exception as e:
            print(f"❌ 测试集生成失败: {e}")
            return []


def demo_single_test(testset_file):
    """演示单个测试"""
    print("\n" + "="*60)
    print("🧪 演示2: 单个测试")
    print("="*60)
    
    controller = KBTestController()
    testsets_dir = config_manager.get('paths.testsets', 'testsets')
    testset_path = os.path.join(testsets_dir, testset_file)
    
    if not os.path.exists(testset_path):
        print(f"❌ 测试集文件不存在: {testset_path}")
        return None
    
    print(f"🧪 开始测试: {testset_file}")
    
    try:
        # 运行测试（使用同步模式和简化评估以加快演示速度）
        result = controller.run_single_test(
            testset_path=testset_path,
            test_name=f"demo_{os.path.splitext(testset_file)[0]}",
            use_async=False,  # 使用同步模式
            use_llm_evaluation=False  # 不使用LLM评估以加快速度
        )
        
        if result:
            stats = result.get('statistics', {})
            print(f"✅ 测试完成!")
            print(f"   📊 总问题数: {stats.get('total_questions', 0)}")
            print(f"   📈 成功率: {stats.get('success_rate', 0):.1%}")
            print(f"   🎯 平均总分: {stats.get('average_scores', {}).get('overall', 0):.2f}")
            
            reports = result.get('generated_reports', {})
            if reports:
                print(f"   📄 生成的报告:")
                for format_type, path in reports.items():
                    print(f"      - {format_type.upper()}: {path}")
            
            return result
        else:
            print("❌ 测试失败")
            return None
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return None


def demo_batch_test(testset_files):
    """演示批量测试"""
    print("\n" + "="*60)
    print("🔄 演示3: 批量测试")
    print("="*60)
    
    if len(testset_files) < 2:
        print("⚠️  测试集数量不足，跳过批量测试演示")
        return
    
    controller = KBTestController()
    
    print(f"🔄 开始批量测试 {len(testset_files)} 个测试集...")
    
    try:
        # 运行批量测试
        results = controller.run_batch_tests(
            use_async=False,  # 使用同步模式
            use_llm_evaluation=False  # 不使用LLM评估以加快速度
        )
        
        if results:
            print(f"✅ 批量测试完成! 共完成 {len(results)} 个测试")
            
            # 计算汇总统计
            total_questions = sum(r.get('statistics', {}).get('total_questions', 0) for r in results)
            avg_success_rate = sum(r.get('statistics', {}).get('success_rate', 0) for r in results) / len(results)
            avg_overall_score = sum(r.get('statistics', {}).get('average_scores', {}).get('overall', 0) for r in results) / len(results)
            
            print(f"   📊 汇总统计:")
            print(f"      总问题数: {total_questions}")
            print(f"      平均成功率: {avg_success_rate:.1%}")
            print(f"      平均总分: {avg_overall_score:.2f}")
            
            return results
        else:
            print("❌ 批量测试失败")
            return None
            
    except Exception as e:
        print(f"❌ 批量测试执行失败: {e}")
        return None


def main():
    """主演示函数"""
    print("🎉 欢迎使用知识库自动化测试系统演示!")
    print("本演示将展示系统的主要功能:")
    print("1. 测试集生成")
    print("2. 单个测试执行")
    print("3. 批量测试执行")
    print("4. 报告生成")
    
    # 启动模拟服务器
    server_process = start_mock_server()
    if not server_process:
        print("❌ 无法启动模拟服务器，演示终止")
        return
    
    try:
        # 演示1: 测试集生成
        testset_files = demo_testset_generation()
        
        if not testset_files:
            print("❌ 无法生成测试集，演示终止")
            return
        
        # 演示2: 单个测试
        single_test_result = demo_single_test(testset_files[0])
        
        # 演示3: 批量测试
        batch_test_results = demo_batch_test(testset_files)
        
        # 总结
        print("\n" + "="*60)
        print("🎊 演示完成!")
        print("="*60)
        
        print("📁 生成的文件:")
        
        # 显示测试集文件
        testsets_dir = config_manager.get('paths.testsets', 'testsets')
        if os.path.exists(testsets_dir):
            print(f"   📝 测试集 ({testsets_dir}):")
            for file in os.listdir(testsets_dir):
                if file.endswith('.json'):
                    print(f"      - {file}")
        
        # 显示报告文件
        reports_dir = config_manager.get('paths.reports', 'reports')
        if os.path.exists(reports_dir):
            print(f"   📊 报告 ({reports_dir}):")
            for file in os.listdir(reports_dir):
                print(f"      - {file}")
        
        # 显示测试结果文件
        results_dir = config_manager.get('paths.test_results', 'test_results')
        if os.path.exists(results_dir):
            print(f"   📋 测试结果 ({results_dir}):")
            for file in os.listdir(results_dir):
                if file.endswith('.json'):
                    print(f"      - {file}")
        
        print("\n💡 提示:")
        print("   - 查看HTML报告以获得最佳的可视化体验")
        print("   - 使用 'python main.py history' 查看测试历史")
        print("   - 使用 'python main.py --help' 查看所有可用命令")
        
    finally:
        # 清理：终止模拟服务器
        if server_process:
            print("\n🛑 正在关闭模拟服务器...")
            server_process.terminate()
            server_process.wait()
            print("✅ 模拟服务器已关闭")


if __name__ == '__main__':
    main()
