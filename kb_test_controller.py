"""
知识库测试主控制器
协调整个自动化测试流程的执行
"""

import os
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from config_manager import config_manager
from testset_generator import TestSetGenerator
from knowledge_base_tester import KnowledgeBaseTester, KnowledgeBaseAPI
from answer_evaluator import AnswerEvaluator
from report_generator import ReportGenerator


class KBTestController:
    """知识库测试主控制器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化测试控制器
        
        Args:
            config_path: 配置文件路径
        """
        if config_path:
            global config_manager
            from config_manager import ConfigManager
            config_manager = ConfigManager(config_path)
        
        self.config = config_manager
        self.setup_logging()
        
        # 初始化各个组件
        self.testset_generator = None
        self.kb_tester = None
        self.answer_evaluator = None
        self.report_generator = None
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("知识库测试控制器初始化完成")
    
    def setup_logging(self):
        """设置日志配置"""
        log_config = self.config.get_logging_config()
        
        # 确保日志目录存在
        log_file = log_config.get('file', 'logs/kb_testing.log')
        log_dir = os.path.dirname(log_file)
        if log_dir:
            Path(log_dir).mkdir(parents=True, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=getattr(logging, log_config.get('level', 'INFO')),
            format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def initialize_components(self):
        """初始化所有组件"""
        try:
            # 初始化测试集生成器
            api_config = self.config.get_api_config('dashscope')
            self.testset_generator = TestSetGenerator(api_config.get('api_key'))
            
            # 初始化知识库测试器
            kb_config = self.config.get_api_config('knowledge_base')
            kb_api = KnowledgeBaseAPI(
                base_url=kb_config.get('base_url', 'http://localhost:3000/api'),
                api_key=kb_config.get('api_key', ''),
                timeout=kb_config.get('timeout', 30)
            )
            self.kb_tester = KnowledgeBaseTester(kb_api)
            
            # 初始化答案评估器
            self.answer_evaluator = AnswerEvaluator()
            
            # 初始化报告生成器
            self.report_generator = ReportGenerator()
            
            self.logger.info("所有组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
            raise
    
    def generate_testset_from_file(self, file_path: str, 
                                 output_path: Optional[str] = None) -> str:
        """
        从文件生成测试集
        
        Args:
            file_path: 输入文件路径
            output_path: 输出文件路径（可选）
            
        Returns:
            生成的测试集文件路径
        """
        if not self.testset_generator:
            self.initialize_components()
        
        if not output_path:
            # 自动生成输出路径
            testsets_dir = self.config.get('paths.testsets', 'testsets')
            filename = os.path.splitext(os.path.basename(file_path))[0]
            output_path = os.path.join(testsets_dir, f"{filename}_testset.json")
        
        self.logger.info(f"开始从文件生成测试集: {file_path}")
        testset = self.testset_generator.generate_from_file(file_path, output_path)
        
        if testset:
            self.logger.info(f"测试集生成完成，包含 {len(testset)} 个问题")
            return output_path
        else:
            self.logger.error("测试集生成失败")
            return ""
    
    def generate_testsets_from_directory(self, input_dir: Optional[str] = None, 
                                       output_dir: Optional[str] = None) -> List[str]:
        """
        从目录批量生成测试集
        
        Args:
            input_dir: 输入目录路径
            output_dir: 输出目录路径
            
        Returns:
            生成的测试集文件路径列表
        """
        if not self.testset_generator:
            self.initialize_components()
        
        if not input_dir:
            input_dir = self.config.get('paths.input_documents', 'data')
        
        if not output_dir:
            output_dir = self.config.get('paths.testsets', 'testsets')
        
        self.logger.info(f"开始批量生成测试集，输入目录: {input_dir}")
        
        generated_files = []
        supported_formats = self.config.get('testset_generation.supported_formats', 
                                          ['.pdf', '.docx', '.html', '.htm'])
        
        for file_name in os.listdir(input_dir):
            file_path = os.path.join(input_dir, file_name)
            if os.path.isfile(file_path):
                ext = os.path.splitext(file_name)[1].lower()
                if ext in supported_formats:
                    output_path = os.path.join(output_dir, 
                                             f"{os.path.splitext(file_name)[0]}_testset.json")
                    try:
                        self.testset_generator.generate_from_file(file_path, output_path)
                        generated_files.append(output_path)
                        self.logger.info(f"已生成测试集: {output_path}")
                    except Exception as e:
                        self.logger.error(f"生成测试集失败 {file_path}: {e}")
        
        self.logger.info(f"批量生成完成，共生成 {len(generated_files)} 个测试集")
        return generated_files
    
    def run_single_test(self, testset_path: str, 
                       test_name: Optional[str] = None,
                       use_async: bool = True,
                       use_llm_evaluation: bool = True) -> Dict[str, Any]:
        """
        运行单个测试集的完整测试流程
        
        Args:
            testset_path: 测试集文件路径
            test_name: 测试名称
            use_async: 是否使用异步测试
            use_llm_evaluation: 是否使用LLM评估
            
        Returns:
            测试结果字典
        """
        if not all([self.kb_tester, self.answer_evaluator, self.report_generator]):
            self.initialize_components()
        
        if not test_name:
            test_name = os.path.splitext(os.path.basename(testset_path))[0]
        
        self.logger.info(f"开始运行测试: {test_name}")
        
        try:
            # 1. 运行知识库测试
            self.logger.info("步骤1: 执行知识库查询测试")
            test_results = self.kb_tester.run_test(testset_path, use_async)
            
            if not test_results:
                self.logger.error("知识库测试失败，无结果返回")
                return {}
            
            # 2. 评估答案质量
            self.logger.info("步骤2: 评估答案质量")
            # 转换测试结果格式以适配评估器
            results_for_evaluation = []
            for result in test_results:
                results_for_evaluation.append({
                    'question': result.question,
                    'expected_answer': result.expected_answer,
                    'actual_answer': result.actual_answer,
                    'success': result.success,
                    'response_time': result.response_time,
                    'error_message': result.error_message,
                    'metadata': result.metadata
                })
            
            evaluated_results = self.answer_evaluator.batch_evaluate(
                results_for_evaluation, use_llm_evaluation
            )
            
            # 3. 计算统计信息
            self.logger.info("步骤3: 计算统计信息")
            statistics = self.answer_evaluator.calculate_statistics(evaluated_results)
            
            # 4. 生成报告
            self.logger.info("步骤4: 生成测试报告")
            generated_reports = self.report_generator.generate_all_reports(
                evaluated_results, statistics, test_name
            )
            
            # 5. 保存测试结果
            results_dir = self.config.get('paths.test_results', 'test_results')
            Path(results_dir).mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = os.path.join(results_dir, f"{test_name}_{timestamp}_results.json")
            
            test_result_data = {
                'test_name': test_name,
                'test_date': datetime.now().isoformat(),
                'testset_path': testset_path,
                'statistics': statistics,
                'evaluated_results': evaluated_results,
                'generated_reports': generated_reports,
                'config_snapshot': {
                    'use_async': use_async,
                    'use_llm_evaluation': use_llm_evaluation,
                    'kb_api_config': self.config.get_api_config('knowledge_base'),
                    'evaluation_config': self.config.get_evaluation_config()
                }
            }
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(test_result_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"测试完成，结果已保存: {results_file}")
            self.logger.info(f"生成的报告: {list(generated_reports.values())}")
            
            return test_result_data
            
        except Exception as e:
            self.logger.error(f"测试执行失败: {e}")
            return {}

    def run_batch_tests(self, testsets_dir: Optional[str] = None,
                       use_async: bool = True,
                       use_llm_evaluation: bool = True) -> List[Dict[str, Any]]:
        """
        批量运行多个测试集

        Args:
            testsets_dir: 测试集目录路径
            use_async: 是否使用异步测试
            use_llm_evaluation: 是否使用LLM评估

        Returns:
            所有测试结果列表
        """
        if not testsets_dir:
            testsets_dir = self.config.get('paths.testsets', 'testsets')

        self.logger.info(f"开始批量测试，测试集目录: {testsets_dir}")

        all_results = []
        testset_files = [f for f in os.listdir(testsets_dir)
                        if f.endswith('.json') and os.path.isfile(os.path.join(testsets_dir, f))]

        for i, testset_file in enumerate(testset_files, 1):
            testset_path = os.path.join(testsets_dir, testset_file)
            test_name = os.path.splitext(testset_file)[0]

            self.logger.info(f"执行测试 {i}/{len(testset_files)}: {test_name}")

            try:
                result = self.run_single_test(
                    testset_path, test_name, use_async, use_llm_evaluation
                )
                if result:
                    all_results.append(result)

            except Exception as e:
                self.logger.error(f"测试失败 {test_name}: {e}")
                continue

        # 生成汇总报告
        if all_results:
            self.logger.info("生成批量测试汇总报告")
            reports_dir = self.config.get('paths.reports', 'reports')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_path = os.path.join(reports_dir, f"batch_test_summary_{timestamp}.json")

            self.report_generator.generate_summary_report(all_results, summary_path)

        self.logger.info(f"批量测试完成，共完成 {len(all_results)} 个测试")
        return all_results
