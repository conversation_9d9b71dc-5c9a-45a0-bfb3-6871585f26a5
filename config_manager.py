"""
配置管理器模块
负责加载和管理系统配置
"""

import yaml
import os
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self._ensure_directories()
    
    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件未找到: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        paths = self.config.get('paths', {})
        for path_name, path_value in paths.items():
            Path(path_value).mkdir(parents=True, exist_ok=True)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键，支持 'api.dashscope.api_key' 格式
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self, path: Optional[str] = None):
        """
        保存配置到文件
        
        Args:
            path: 保存路径，默认为原配置文件路径
        """
        save_path = path or self.config_path
        with open(save_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, 
                     allow_unicode=True, indent=2)
    
    def get_api_config(self, service: str) -> Dict[str, Any]:
        """
        获取API配置
        
        Args:
            service: 服务名称 (如 'dashscope', 'knowledge_base')
            
        Returns:
            API配置字典
        """
        return self.get(f'api.{service}', {})
    
    def get_testset_config(self) -> Dict[str, Any]:
        """获取测试集生成配置"""
        return self.get('testset_generation', {})
    
    def get_testing_config(self) -> Dict[str, Any]:
        """获取知识库测试配置"""
        return self.get('knowledge_base_testing', {})
    
    def get_evaluation_config(self) -> Dict[str, Any]:
        """获取答案评估配置"""
        return self.get('answer_evaluation', {})
    
    def get_paths_config(self) -> Dict[str, str]:
        """获取路径配置"""
        return self.get('paths', {})
    
    def get_reporting_config(self) -> Dict[str, Any]:
        """获取报告配置"""
        return self.get('reporting', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get('logging', {})
    
    def validate_config(self) -> bool:
        """
        验证配置的完整性
        
        Returns:
            配置是否有效
        """
        required_keys = [
            'api.dashscope.api_key',
            'testset_generation.chunk_size',
            'paths.input_documents',
            'paths.testsets',
            'paths.test_results'
        ]
        
        for key in required_keys:
            if self.get(key) is None:
                print(f"缺少必需的配置项: {key}")
                return False
        
        return True


# 全局配置管理器实例
config_manager = ConfigManager()
