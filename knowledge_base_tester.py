"""
知识库测试器模块
负责使用测试集对知识库进行自动化测试
"""

import json
import time
import requests
import asyncio
import aiohttp
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
from config_manager import config_manager


@dataclass
class TestQuestion:
    """测试问题数据类"""
    question: str
    expected_answer: str
    difficulty: str
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class TestResult:
    """测试结果数据类"""
    question: str
    expected_answer: str
    actual_answer: str
    response_time: float
    success: bool
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class KnowledgeBaseAPI:
    """知识库API接口类"""
    
    def __init__(self, base_url: str, api_key: str, timeout: int = 30):
        """
        初始化知识库API
        
        Args:
            base_url: API基础URL
            api_key: API密钥
            timeout: 超时时间
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        })
    
    def query(self, question: str, **kwargs) -> Tuple[str, bool, Optional[str]]:
        """
        查询知识库
        
        Args:
            question: 问题
            **kwargs: 其他查询参数
            
        Returns:
            (答案, 是否成功, 错误信息)
        """
        try:
            # 这里是一个示例实现，您需要根据实际的知识库API调整
            payload = {
                'question': question,
                'top_k': kwargs.get('top_k', 5),
                'score_threshold': kwargs.get('score_threshold', 0.7)
            }
            
            response = self.session.post(
                f'{self.base_url}/query',
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get('answer', '')
                return answer, True, None
            else:
                return '', False, f'HTTP {response.status_code}: {response.text}'
                
        except requests.exceptions.RequestException as e:
            return '', False, str(e)
    
    async def async_query(self, session: aiohttp.ClientSession, question: str, **kwargs) -> Tuple[str, bool, Optional[str]]:
        """
        异步查询知识库
        
        Args:
            session: aiohttp会话
            question: 问题
            **kwargs: 其他查询参数
            
        Returns:
            (答案, 是否成功, 错误信息)
        """
        try:
            payload = {
                'question': question,
                'top_k': kwargs.get('top_k', 5),
                'score_threshold': kwargs.get('score_threshold', 0.7)
            }
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            async with session.post(
                f'{self.base_url}/query',
                json=payload,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    answer = data.get('answer', '')
                    return answer, True, None
                else:
                    text = await response.text()
                    return '', False, f'HTTP {response.status}: {text}'
                    
        except Exception as e:
            return '', False, str(e)


class KnowledgeBaseTester:
    """知识库测试器类"""
    
    def __init__(self, kb_api: Optional[KnowledgeBaseAPI] = None):
        """
        初始化知识库测试器
        
        Args:
            kb_api: 知识库API实例
        """
        self.config = config_manager.get_testing_config()
        self.query_params = self.config.get('query_params', {})
        
        if kb_api is None:
            kb_config = config_manager.get_api_config('knowledge_base')
            self.kb_api = KnowledgeBaseAPI(
                base_url=kb_config.get('base_url', 'http://localhost:3000/api'),
                api_key=kb_config.get('api_key', ''),
                timeout=kb_config.get('timeout', 30)
            )
        else:
            self.kb_api = kb_api
        
        self.logger = logging.getLogger(__name__)
    
    def load_testset(self, testset_path: str) -> List[TestQuestion]:
        """
        加载测试集
        
        Args:
            testset_path: 测试集文件路径
            
        Returns:
            测试问题列表
        """
        try:
            with open(testset_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            questions = []
            for item in data:
                question = TestQuestion(
                    question=item['question'],
                    expected_answer=item['answer'],
                    difficulty=item.get('difficulty', 'medium'),
                    metadata=item.get('metadata', {})
                )
                questions.append(question)
            
            return questions
            
        except Exception as e:
            self.logger.error(f"加载测试集失败: {e}")
            return []
    
    def test_single_question(self, test_question: TestQuestion) -> TestResult:
        """
        测试单个问题
        
        Args:
            test_question: 测试问题
            
        Returns:
            测试结果
        """
        start_time = time.time()
        
        try:
            answer, success, error_msg = self.kb_api.query(
                test_question.question, 
                **self.query_params
            )
            
            response_time = time.time() - start_time
            
            return TestResult(
                question=test_question.question,
                expected_answer=test_question.expected_answer,
                actual_answer=answer,
                response_time=response_time,
                success=success,
                error_message=error_msg,
                metadata=test_question.metadata
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                question=test_question.question,
                expected_answer=test_question.expected_answer,
                actual_answer='',
                response_time=response_time,
                success=False,
                error_message=str(e),
                metadata=test_question.metadata
            )
    
    def test_batch_sync(self, test_questions: List[TestQuestion]) -> List[TestResult]:
        """
        同步批量测试
        
        Args:
            test_questions: 测试问题列表
            
        Returns:
            测试结果列表
        """
        results = []
        max_workers = self.config.get('parallel_workers', 3)
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_question = {
                executor.submit(self.test_single_question, q): q 
                for q in test_questions
            }
            
            for future in as_completed(future_to_question):
                try:
                    result = future.result()
                    results.append(result)
                    self.logger.info(f"完成测试: {result.question[:50]}...")
                except Exception as e:
                    question = future_to_question[future]
                    self.logger.error(f"测试失败: {question.question[:50]}... - {e}")
        
        return results

    async def test_batch_async(self, test_questions: List[TestQuestion]) -> List[TestResult]:
        """
        异步批量测试

        Args:
            test_questions: 测试问题列表

        Returns:
            测试结果列表
        """
        results = []
        batch_size = self.config.get('batch_size', 10)

        async with aiohttp.ClientSession() as session:
            for i in range(0, len(test_questions), batch_size):
                batch = test_questions[i:i + batch_size]
                batch_results = await self._test_batch_chunk_async(session, batch)
                results.extend(batch_results)

                # 添加延迟避免过于频繁的请求
                if i + batch_size < len(test_questions):
                    await asyncio.sleep(0.1)

        return results

    async def _test_batch_chunk_async(self, session: aiohttp.ClientSession,
                                    test_questions: List[TestQuestion]) -> List[TestResult]:
        """
        异步测试一个批次的问题

        Args:
            session: aiohttp会话
            test_questions: 测试问题列表

        Returns:
            测试结果列表
        """
        tasks = []
        for question in test_questions:
            task = self._test_single_question_async(session, question)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                question = test_questions[i]
                processed_results.append(TestResult(
                    question=question.question,
                    expected_answer=question.expected_answer,
                    actual_answer='',
                    response_time=0.0,
                    success=False,
                    error_message=str(result),
                    metadata=question.metadata
                ))
            else:
                processed_results.append(result)

        return processed_results

    async def _test_single_question_async(self, session: aiohttp.ClientSession,
                                        test_question: TestQuestion) -> TestResult:
        """
        异步测试单个问题

        Args:
            session: aiohttp会话
            test_question: 测试问题

        Returns:
            测试结果
        """
        start_time = time.time()

        try:
            answer, success, error_msg = await self.kb_api.async_query(
                session, test_question.question, **self.query_params
            )

            response_time = time.time() - start_time

            return TestResult(
                question=test_question.question,
                expected_answer=test_question.expected_answer,
                actual_answer=answer,
                response_time=response_time,
                success=success,
                error_message=error_msg,
                metadata=test_question.metadata
            )

        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                question=test_question.question,
                expected_answer=test_question.expected_answer,
                actual_answer='',
                response_time=response_time,
                success=False,
                error_message=str(e),
                metadata=test_question.metadata
            )

    def run_test(self, testset_path: str, use_async: bool = True) -> List[TestResult]:
        """
        运行完整测试

        Args:
            testset_path: 测试集文件路径
            use_async: 是否使用异步测试

        Returns:
            测试结果列表
        """
        self.logger.info(f"开始加载测试集: {testset_path}")
        test_questions = self.load_testset(testset_path)

        if not test_questions:
            self.logger.error("测试集为空或加载失败")
            return []

        self.logger.info(f"加载了 {len(test_questions)} 个测试问题")

        if use_async:
            self.logger.info("使用异步模式进行测试")
            return asyncio.run(self.test_batch_async(test_questions))
        else:
            self.logger.info("使用同步模式进行测试")
            return self.test_batch_sync(test_questions)

    def save_results(self, results: List[TestResult], output_path: str):
        """
        保存测试结果

        Args:
            results: 测试结果列表
            output_path: 输出文件路径
        """
        try:
            results_data = []
            for result in results:
                results_data.append({
                    'question': result.question,
                    'expected_answer': result.expected_answer,
                    'actual_answer': result.actual_answer,
                    'response_time': result.response_time,
                    'success': result.success,
                    'error_message': result.error_message,
                    'metadata': result.metadata
                })

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"测试结果已保存到: {output_path}")

        except Exception as e:
            self.logger.error(f"保存测试结果失败: {e}")
