"""
测试报告生成器模块
负责生成详细的测试报告
"""

import json
import csv
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging
from config_manager import config_manager


class ReportGenerator:
    """测试报告生成器类"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.config = config_manager.get_reporting_config()
        self.paths_config = config_manager.get_paths_config()
        self.logger = logging.getLogger(__name__)
        
        # 确保报告目录存在
        reports_dir = self.paths_config.get('reports', 'reports')
        Path(reports_dir).mkdir(parents=True, exist_ok=True)
    
    def generate_html_report(self, evaluated_results: List[Dict[str, Any]], 
                           statistics: Dict[str, Any], 
                           output_path: str) -> str:
        """
        生成HTML格式的测试报告
        
        Args:
            evaluated_results: 评估结果列表
            statistics: 统计信息
            output_path: 输出文件路径
            
        Returns:
            生成的报告文件路径
        """
        try:
            html_content = self._generate_html_content(evaluated_results, statistics)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"HTML报告已生成: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {e}")
            return ""
    
    def _generate_html_content(self, evaluated_results: List[Dict[str, Any]], 
                              statistics: Dict[str, Any]) -> str:
        """
        生成HTML报告内容
        
        Args:
            evaluated_results: 评估结果列表
            statistics: 统计信息
            
        Returns:
            HTML内容字符串
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 生成统计图表数据
        score_dist = statistics.get('score_distribution', {})
        avg_scores = statistics.get('average_scores', {})
        
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库测试报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }}
        .summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .summary-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 14px;
            opacity: 0.9;
        }}
        .summary-card .value {{
            font-size: 24px;
            font-weight: bold;
        }}
        .charts-section {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }}
        .chart-container {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }}
        .results-table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        .results-table th, .results-table td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        .results-table th {{
            background-color: #f8f9fa;
            font-weight: bold;
        }}
        .score-badge {{
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
        }}
        .score-excellent {{ background-color: #28a745; }}
        .score-good {{ background-color: #17a2b8; }}
        .score-fair {{ background-color: #ffc107; color: #000; }}
        .score-poor {{ background-color: #dc3545; }}
        .question-cell {{
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }}
        .expandable {{
            cursor: pointer;
        }}
        .expandable:hover {{
            background-color: #f8f9fa;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>知识库测试报告</h1>
            <p>生成时间: {timestamp}</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总问题数</h3>
                <div class="value">{statistics.get('total_questions', 0)}</div>
            </div>
            <div class="summary-card">
                <h3>成功查询数</h3>
                <div class="value">{statistics.get('successful_queries', 0)}</div>
            </div>
            <div class="summary-card">
                <h3>成功率</h3>
                <div class="value">{statistics.get('success_rate', 0):.1%}</div>
            </div>
            <div class="summary-card">
                <h3>平均总分</h3>
                <div class="value">{avg_scores.get('overall', 0):.2f}</div>
            </div>
        </div>
        
        <div class="charts-section">
            <div class="chart-container">
                <h3>分数分布</h3>
                <canvas id="scoreDistChart" width="400" height="300"></canvas>
            </div>
            <div class="chart-container">
                <h3>各维度平均分</h3>
                <canvas id="avgScoresChart" width="400" height="300"></canvas>
            </div>
        </div>
        
        <div class="results-section">
            <h2>详细测试结果</h2>
            <table class="results-table">
                <thead>
                    <tr>
                        <th>问题</th>
                        <th>成功</th>
                        <th>总分</th>
                        <th>相似度</th>
                        <th>相关性</th>
                        <th>完整性</th>
                        <th>准确性</th>
                        <th>响应时间(s)</th>
                    </tr>
                </thead>
                <tbody>
                    {self._generate_results_rows(evaluated_results)}
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        // 分数分布图表
        const scoreDistCtx = document.getElementById('scoreDistChart').getContext('2d');
        new Chart(scoreDistCtx, {{
            type: 'doughnut',
            data: {{
                labels: ['优秀(≥0.8)', '良好(0.6-0.8)', '一般(0.4-0.6)', '较差(<0.4)'],
                datasets: [{{
                    data: [{score_dist.get('excellent', 0)}, {score_dist.get('good', 0)}, 
                           {score_dist.get('fair', 0)}, {score_dist.get('poor', 0)}],
                    backgroundColor: ['#28a745', '#17a2b8', '#ffc107', '#dc3545']
                }}]
            }},
            options: {{
                responsive: true,
                maintainAspectRatio: false
            }}
        }});
        
        // 各维度平均分图表
        const avgScoresCtx = document.getElementById('avgScoresChart').getContext('2d');
        new Chart(avgScoresCtx, {{
            type: 'radar',
            data: {{
                labels: ['相似度', '相关性', '完整性', '准确性'],
                datasets: [{{
                    label: '平均分',
                    data: [{avg_scores.get('similarity', 0):.2f}, {avg_scores.get('relevance', 0):.2f}, 
                           {avg_scores.get('completeness', 0):.2f}, {avg_scores.get('accuracy', 0):.2f}],
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    pointBackgroundColor: 'rgba(54, 162, 235, 1)'
                }}]
            }},
            options: {{
                responsive: true,
                maintainAspectRatio: false,
                scales: {{
                    r: {{
                        beginAtZero: true,
                        max: 1
                    }}
                }}
            }}
        }});
        
        // 点击展开功能
        document.querySelectorAll('.expandable').forEach(row => {{
            row.addEventListener('click', function() {{
                const questionCell = this.querySelector('.question-cell');
                if (questionCell.style.whiteSpace === 'normal') {{
                    questionCell.style.whiteSpace = 'nowrap';
                    questionCell.style.overflow = 'hidden';
                    questionCell.style.textOverflow = 'ellipsis';
                }} else {{
                    questionCell.style.whiteSpace = 'normal';
                    questionCell.style.overflow = 'visible';
                    questionCell.style.textOverflow = 'unset';
                }}
            }});
        }});
    </script>
</body>
</html>
        """
        
        return html_template
    
    def _generate_results_rows(self, evaluated_results: List[Dict[str, Any]]) -> str:
        """
        生成结果表格行
        
        Args:
            evaluated_results: 评估结果列表
            
        Returns:
            HTML表格行字符串
        """
        rows = []
        for result in evaluated_results:
            evaluation = result.get('evaluation', {})
            overall_score = evaluation.get('overall', 0)
            
            # 确定分数等级
            if overall_score >= 0.8:
                score_class = 'score-excellent'
            elif overall_score >= 0.6:
                score_class = 'score-good'
            elif overall_score >= 0.4:
                score_class = 'score-fair'
            else:
                score_class = 'score-poor'
            
            success_icon = "✅" if result.get('success', False) else "❌"
            
            row = f"""
                <tr class="expandable">
                    <td class="question-cell">{result.get('question', '')[:100]}...</td>
                    <td>{success_icon}</td>
                    <td><span class="score-badge {score_class}">{overall_score:.2f}</span></td>
                    <td>{evaluation.get('similarity', 0):.2f}</td>
                    <td>{evaluation.get('relevance', 0):.2f}</td>
                    <td>{evaluation.get('completeness', 0):.2f}</td>
                    <td>{evaluation.get('accuracy', 0):.2f}</td>
                    <td>{result.get('response_time', 0):.2f}</td>
                </tr>
            """
            rows.append(row)
        
        return ''.join(rows)

    def generate_json_report(self, evaluated_results: List[Dict[str, Any]],
                           statistics: Dict[str, Any],
                           output_path: str) -> str:
        """
        生成JSON格式的测试报告

        Args:
            evaluated_results: 评估结果列表
            statistics: 统计信息
            output_path: 输出文件路径

        Returns:
            生成的报告文件路径
        """
        try:
            report_data = {
                'metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'total_questions': len(evaluated_results),
                    'report_version': '1.0'
                },
                'statistics': statistics,
                'results': evaluated_results
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"JSON报告已生成: {output_path}")
            return output_path

        except Exception as e:
            self.logger.error(f"生成JSON报告失败: {e}")
            return ""

    def generate_csv_report(self, evaluated_results: List[Dict[str, Any]],
                          output_path: str) -> str:
        """
        生成CSV格式的测试报告

        Args:
            evaluated_results: 评估结果列表
            output_path: 输出文件路径

        Returns:
            生成的报告文件路径
        """
        try:
            with open(output_path, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)

                # 写入表头
                headers = [
                    '问题', '期望答案', '实际答案', '成功', '响应时间(秒)',
                    '相似度', '相关性', '完整性', '准确性', '总分', '评价理由', '错误信息'
                ]
                writer.writerow(headers)

                # 写入数据行
                for result in evaluated_results:
                    evaluation = result.get('evaluation', {})
                    row = [
                        result.get('question', ''),
                        result.get('expected_answer', ''),
                        result.get('actual_answer', ''),
                        '是' if result.get('success', False) else '否',
                        result.get('response_time', 0),
                        evaluation.get('similarity', 0),
                        evaluation.get('relevance', 0),
                        evaluation.get('completeness', 0),
                        evaluation.get('accuracy', 0),
                        evaluation.get('overall', 0),
                        evaluation.get('reason', ''),
                        result.get('error_message', '')
                    ]
                    writer.writerow(row)

            self.logger.info(f"CSV报告已生成: {output_path}")
            return output_path

        except Exception as e:
            self.logger.error(f"生成CSV报告失败: {e}")
            return ""

    def generate_all_reports(self, evaluated_results: List[Dict[str, Any]],
                           statistics: Dict[str, Any],
                           base_filename: str) -> Dict[str, str]:
        """
        生成所有格式的报告

        Args:
            evaluated_results: 评估结果列表
            statistics: 统计信息
            base_filename: 基础文件名（不含扩展名）

        Returns:
            生成的报告文件路径字典
        """
        reports_dir = self.paths_config.get('reports', 'reports')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        generated_reports = {}
        formats = self.config.get('formats', ['html', 'json', 'csv'])

        for format_type in formats:
            filename = f"{base_filename}_{timestamp}.{format_type}"
            output_path = os.path.join(reports_dir, filename)

            if format_type == 'html':
                path = self.generate_html_report(evaluated_results, statistics, output_path)
            elif format_type == 'json':
                path = self.generate_json_report(evaluated_results, statistics, output_path)
            elif format_type == 'csv':
                path = self.generate_csv_report(evaluated_results, output_path)
            else:
                self.logger.warning(f"不支持的报告格式: {format_type}")
                continue

            if path:
                generated_reports[format_type] = path

        return generated_reports

    def generate_summary_report(self, multiple_test_results: List[Dict[str, Any]],
                              output_path: str) -> str:
        """
        生成多个测试的汇总报告

        Args:
            multiple_test_results: 多个测试结果列表
            output_path: 输出文件路径

        Returns:
            生成的报告文件路径
        """
        try:
            summary_data = {
                'metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'total_tests': len(multiple_test_results),
                    'summary_version': '1.0'
                },
                'test_summaries': []
            }

            for test_result in multiple_test_results:
                test_summary = {
                    'test_name': test_result.get('test_name', '未知测试'),
                    'test_date': test_result.get('test_date', ''),
                    'statistics': test_result.get('statistics', {}),
                    'file_path': test_result.get('file_path', '')
                }
                summary_data['test_summaries'].append(test_summary)

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"汇总报告已生成: {output_path}")
            return output_path

        except Exception as e:
            self.logger.error(f"生成汇总报告失败: {e}")
            return ""
