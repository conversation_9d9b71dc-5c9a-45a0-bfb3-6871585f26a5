# 知识库自动化测试系统配置文件

# API配置
api:
  dashscope:
    api_key: "sk-2374f1a49b0548c8bba5fa63d85d04a5"
    model: "qwen-max"
    embedding_model: "text-embedding-v1"
  
  # 知识库API配置（示例）
  knowledge_base:
    base_url: "http://localhost:3000/api"
    api_key: "your_kb_api_key"
    timeout: 30

# 测试集生成配置
testset_generation:
  chunk_size: 1000
  chunk_overlap: 200
  questions_per_chunk: 3
  supported_formats: [".pdf", ".docx", ".html", ".htm", ".txt"]
  
  # 问题生成提示词模板
  question_prompt_template: |
    请根据以下文本内容生成{num_questions}个高质量的问答对，包含不同难度和类型的问题：
    
    文本内容：
    {text_chunk}
    
    请按以下格式返回：
    Q1: [问题1]
    A1: [答案1]
    Q2: [问题2]
    A2: [答案2]
    Q3: [问题3]
    A3: [答案3]

# 知识库测试配置
knowledge_base_testing:
  max_retries: 3
  retry_delay: 1  # 秒
  batch_size: 10
  parallel_workers: 3
  
  # 查询参数
  query_params:
    top_k: 5
    score_threshold: 0.7
    max_tokens: 1000

# 答案评估配置
answer_evaluation:
  similarity_threshold: 0.8
  evaluation_metrics: ["similarity", "relevance", "completeness", "accuracy"]
  
  # 评估提示词模板
  evaluation_prompt_template: |
    请评估以下答案的质量：
    
    问题：{question}
    标准答案：{expected_answer}
    实际答案：{actual_answer}
    
    请从以下维度评分（1-10分）：
    1. 相似度：实际答案与标准答案的相似程度
    2. 相关性：答案是否回答了问题
    3. 完整性：答案是否完整
    4. 准确性：答案是否准确
    
    请按以下格式返回：
    相似度: [分数]
    相关性: [分数]
    完整性: [分数]
    准确性: [分数]
    总体评分: [分数]
    评价理由: [简要说明]

# 文件路径配置
paths:
  input_documents: "data"
  testsets: "testsets"
  test_results: "test_results"
  reports: "reports"
  logs: "logs"

# 报告配置
reporting:
  formats: ["html", "json", "csv"]
  include_charts: true
  include_detailed_results: true
  
  # 报告模板
  html_template: "templates/report_template.html"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/kb_testing.log"
  max_file_size: "10MB"
  backup_count: 5

# 性能配置
performance:
  max_memory_usage: "2GB"
  cache_enabled: true
  cache_size: 1000
  
# 质量控制配置
quality_control:
  min_question_length: 10
  max_question_length: 200
  min_answer_length: 5
  max_answer_length: 500
  duplicate_threshold: 0.9
