import os
import json
import pdfplumber
from langchain.text_splitter import RecursiveCharacterTextSplitter
import dashscope
from dashscope import Generation
from typing import List, Dict, Any
from docx import Document
from bs4 import BeautifulSoup

class TestSetGenerator:
    def __init__(self, api_key: str):
        """
        初始化测试集生成器
        :param api_key: DashScope API密钥
        """
        dashscope.api_key = api_key
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200
        )
    
    def extract_text(self, file_path: str) -> str:
        ext = os.path.splitext(file_path)[1].lower()
        if ext == '.pdf':
            return self.extract_text_from_pdf(file_path)
        elif ext == '.docx':
            return self.extract_text_from_docx(file_path)
        elif ext == '.html' or ext == '.htm':
            return self.extract_text_from_html(file_path)
        else:
            raise ValueError(f"不支持的文件类型: {ext}")
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        从PDF文件中提取文本内容
        :param pdf_path: PDF文件路径
        :return: 提取的文本内容
        """
        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                text += page.extract_text()
        return text
    
    def extract_text_from_docx(self, docx_path: str) -> str:
        doc = Document(docx_path)
        return '\n'.join([para.text for para in doc.paragraphs])
    
    def extract_text_from_html(self, html_path: str) -> str:
        with open(html_path, 'r', encoding='utf-8') as f:
            soup = BeautifulSoup(f, 'html.parser')
            return soup.get_text(separator='\n')
    
    def chunk_text(self, text: str) -> List[str]:
        """
        将长文本分割成适合处理的块
        :param text: 输入文本
        :return: 文本块列表
        """
        return self.text_splitter.split_text(text)
    
    def generate_qa_pairs(self, text_chunk: str, num_questions: int = 3) -> List[Dict[str, str]]:
        """
        为文本块生成问答对
        :param text_chunk: 文本内容块
        :param num_questions: 要生成的问题数量
        :return: 问答对列表
        """
        response = Generation.call(
            model='qwen-max',
            prompt=f'''请根据以下文本内容生成{num_questions}个高质量的问答对，包含不同难度和类型的问题：
            
文本内容：
{text_chunk}

请按以下格式返回：
Q1: [问题1]
A1: [答案1]
Q2: [问题2]
A2: [答案2]
Q3: [问题3]
A3: [答案3]'''
        )
        return self._parse_qa_pairs(response.output.text)
    
    def _parse_qa_pairs(self, text: str) -> List[Dict[str, str]]:
        """
        解析生成的问答对文本为结构化数据
        :param text: 生成的问答文本
        :return: 结构化的问答对列表
        """
        qa_pairs = []
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        current_q = None
        for line in lines:
            if line.startswith('Q') and ':' in line:
                current_q = line.split(':', 1)[1].strip()
            elif line.startswith('A') and ':' in line and current_q:
                answer = line.split(':', 1)[1].strip()
                qa_pairs.append({
                    'question': current_q,
                    'answer': answer,
                    'difficulty': self._estimate_difficulty(current_q, answer)
                })
                current_q = None
        return qa_pairs
    
    def _estimate_difficulty(self, question: str, answer: str) -> str:
        """
        估计问题的难度等级
        :param question: 问题文本
        :param answer: 答案文本
        :return: 难度等级 (easy/medium/hard)
        """
        response = Generation.call(
            model='qwen-max',
            prompt=f'''评估以下问题的难度等级（easy/medium/hard）：
            问题: {question}
            答案: {answer}
            
            考虑因素：所需知识深度、推理复杂度、答案明确性'''
        )
        difficulty = response.output.text.lower().strip()
        return difficulty if difficulty in ['easy', 'medium', 'hard'] else 'medium'
    
    def deduplicate_testset(self, testset: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        去重测试集
        :param testset: 原始测试集
        :return: 去重后的测试集
        """
        seen = set()
        deduplicated = []
        for item in testset:
            key = (item['question'].lower(), item['answer'].lower())
            if key not in seen:
                seen.add(key)
                deduplicated.append(item)
        return deduplicated
    
    def validate_question(self, question: Dict[str, str], original_text: str) -> bool:
        """
        验证问题是否可以从原文中得到答案
        :param question: 问答对
        :param original_text: 原始文本
        :return: 是否有效
        """
        response = Generation.call(
            model='qwen-max',
            prompt=f'''验证以下问题是否可以从给定文本中得到合理答案：
            
问题：{question['question']}
答案：{question['answer']}
            
文本片段（供参考）：
{original_text[:1500]}... [截断]
            
请回答：1. 是否相关（是/否） 2. 简要理由'''
        )
        return '是' in response.output.text
    
    def generate_from_file(self, file_path: str, output_path: str = None) -> List[Dict[str, Any]]:
        """
        从文件生成完整测试集的主方法
        :param file_path: 文件路径
        :param output_path: 输出文件路径（可选）
        :return: 生成的测试集
        """
        print(f"开始处理文件: {file_path}")
        raw_text = self.extract_text(file_path)
        print(f"已提取文本，长度: {len(raw_text)}字符")
        chunks = self.chunk_text(raw_text)
        print(f"已分块，共{len(chunks)}个文本块")
        full_testset = []
        for i, chunk in enumerate(chunks, 1):
            print(f"正在处理第{i}/{len(chunks)}个文本块...")
            try:
                qa_pairs = self.generate_qa_pairs(chunk)
                valid_pairs = [pair for pair in qa_pairs if self.validate_question(pair, raw_text)]
                full_testset.extend(valid_pairs)
            except Exception as e:
                print(f"处理第{i}个文本块时出错: {str(e)}")
                continue
        print("正在进行后处理...")
        deduplicated = self.deduplicate_testset(full_testset)
        if output_path:
            dir_name = os.path.dirname(output_path)
            if dir_name and not os.path.exists(dir_name):
                os.makedirs(dir_name)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(deduplicated, f, ensure_ascii=False, indent=2)
            print(f"测试集已保存至: {output_path}")
        return deduplicated

    def process_directory(self, dir_path: str, output_dir: str = None):
        files = [f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))]
        for file in files:
            ext = os.path.splitext(file)[1].lower()
            if ext in ['.pdf', '.docx', '.html', '.htm']:
                input_path = os.path.join(dir_path, file)
                output_path = None
                if output_dir:
                    if not os.path.exists(output_dir):
                        os.makedirs(output_dir)
                    output_path = os.path.join(output_dir, f"{os.path.splitext(file)[0]}_testset.json")
                self.generate_from_file(input_path, output_path)

if __name__ == "__main__":
    API_KEY = "sk-2374f1a49b0548c8bba5fa63d85d04a5"  # 替换为你的实际API密钥
    DIR_PATH = "data"  # data 目录
    OUTPUT_DIR = "testsets"  # 输出目录
    generator = TestSetGenerator(API_KEY)
    generator.process_directory(DIR_PATH, OUTPUT_DIR)