public class TokenUsage {
    private int prompt_tokens;
    private String prompt_unit_price;
    private String prompt_price_unit;
    private String prompt_price;
    private int completion_tokens;
    private String completion_unit_price;
    private String completion_price_unit;
    private String completion_price;
    private int total_tokens;
    private String total_price;
    private String currency;
    private double latency;

    // getter 和 setter 方法
    public int getPrompt_tokens() { return prompt_tokens; }
    public void setPrompt_tokens(int prompt_tokens) { this.prompt_tokens = prompt_tokens; }

    public String getPrompt_unit_price() { return prompt_unit_price; }
    public void setPrompt_unit_price(String prompt_unit_price) { this.prompt_unit_price = prompt_unit_price; }

    public String getPrompt_price_unit() { return prompt_price_unit; }
    public void setPrompt_price_unit(String prompt_price_unit) { this.prompt_price_unit = prompt_price_unit; }

    public String getPrompt_price() { return prompt_price; }
    public void setPrompt_price(String prompt_price) { this.prompt_price = prompt_price; }

    public int getCompletion_tokens() { return completion_tokens; }
    public void setCompletion_tokens(int completion_tokens) { this.completion_tokens = completion_tokens; }

    public String getCompletion_unit_price() { return completion_unit_price; }
    public void setCompletion_unit_price(String completion_unit_price) { this.completion_unit_price = completion_unit_price; }

    public String getCompletion_price_unit() { return completion_price_unit; }
    public void setCompletion_price_unit(String completion_price_unit) { this.completion_price_unit = completion_price_unit; }

    public String getCompletion_price() { return completion_price; }
    public void setCompletion_price(String completion_price) { this.completion_price = completion_price; }

    public int getTotal_tokens() { return total_tokens; }
    public void setTotal_tokens(int total_tokens) { this.total_tokens = total_tokens; }

    public String getTotal_price() { return total_price; }
    public void setTotal_price(String total_price) { this.total_price = total_price; }

    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }

    public double getLatency() { return latency; }
    public void setLatency(double latency) { this.latency = latency; }
} 